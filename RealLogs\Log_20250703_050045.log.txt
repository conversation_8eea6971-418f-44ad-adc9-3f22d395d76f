Log started at 7/3/2025 5:00:45 AM
2025-07-03 05:00:45.376 [Information] LoggingService: Logging service initialized
2025-07-03 05:00:45.383 [Information] App: Verbose logging enabled
2025-07-03 05:00:45.389 [Information] AppConfigurationService: Initializing configuration service
2025-07-03 05:00:45.389 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-07-03 05:00:45.428 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config\app_config.json
2025-07-03 05:00:45.430 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-03 05:00:45.430 [Information] App: Configuration service initialized successfully
2025-07-03 05:00:45.431 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-03 05:00:45.431 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-03 05:00:45.435 [Information] App: Environment variable exists: True, not 'false': False
2025-07-03 05:00:45.436 [Information] App: Final useDummyImplementations value: False
2025-07-03 05:00:45.436 [Information] App: Updating config to NOT use dummy implementations
2025-07-03 05:00:45.438 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-03 05:00:45.449 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config\app_config.json
2025-07-03 05:00:45.450 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-03 05:00:45.451 [Information] App: usePatchedImplementation flag is: True
2025-07-03 05:00:45.451 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-03 05:00:45.452 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries'
2025-07-03 05:00:45.452 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-03 05:00:45.452 [Information] App: verboseLogging flag is: True
2025-07-03 05:00:45.454 [Information] App: Verifying real hardware requirements...
2025-07-03 05:00:45.454 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-03 05:00:45.454 [Information] App: ✓ Found critical library: apci.dll
2025-07-03 05:00:45.455 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-03 05:00:45.455 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-03 05:00:45.455 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 05:00:45.455 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-03 05:00:45.456 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-07-03 05:00:45.456 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-03 05:00:45.468 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-03 05:00:45.468 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-03 05:00:45.469 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-03 05:00:45.470 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-03 05:00:45.470 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoFlashWR.Communication.dll
2025-07-03 05:00:45.482 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-03 05:00:45.483 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\patched_factory_created.txt
2025-07-03 05:00:45.483 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-03 05:00:45.484 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-03 05:00:45.484 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-03 05:00:45.513 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-03 05:00:45.516 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-03 05:00:45.516 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-03 05:00:45.517 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-03 05:00:45.517 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-03 05:00:45.517 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-03 05:00:45.518 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-03 05:00:45.520 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-03 05:00:45.522 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-03 05:00:45.522 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 05:00:45.523 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-03 05:00:45.528 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\apci.dll
2025-07-03 05:00:45.530 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-07-03 05:00:45.531 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\apcidb.dll
2025-07-03 05:00:45.533 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apcidb.dll
2025-07-03 05:00:45.536 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-03 05:00:45.556 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlus.dll
2025-07-03 05:00:45.558 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-03 05:00:45.560 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlusData.dll
2025-07-03 05:00:45.560 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-03 05:00:45.561 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlusTea2Data.dll
2025-07-03 05:00:45.563 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-03 05:00:45.564 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NAMS.AC.Services.Interface.dll
2025-07-03 05:00:45.565 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-03 05:00:45.566 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-03 05:00:45.567 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-03 05:00:45.568 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Core.dll
2025-07-03 05:00:45.569 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-03 05:00:45.570 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Logging.dll
2025-07-03 05:00:45.571 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-03 05:00:45.572 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Persistence.dll
2025-07-03 05:00:45.572 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-03 05:00:45.573 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.NVS.Persistence.NHibernate.dll
2025-07-03 05:00:45.575 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-03 05:00:45.576 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Baf.Utility.dll
2025-07-03 05:00:45.577 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-03 05:00:45.578 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-03 05:00:45.579 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-03 05:00:45.580 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Waf.ServiceContract.dll
2025-07-03 05:00:45.581 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-03 05:00:45.582 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\VolvoIt.Waf.Utility.dll
2025-07-03 05:00:45.583 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-03 05:00:45.584 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlus.dll.config
2025-07-03 05:00:45.585 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-03 05:00:45.586 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Volvo.ApciPlusData.dll.config
2025-07-03 05:00:45.589 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-03 05:00:45.592 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\NHibernate.dll
2025-07-03 05:00:45.594 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-03 05:00:45.595 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\NHibernate.Caches.SysCache2.dll
2025-07-03 05:00:45.596 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-03 05:00:45.597 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Iesi.Collections.dll
2025-07-03 05:00:45.598 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-03 05:00:45.599 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Ionic.Zip.Reduced.dll
2025-07-03 05:00:45.601 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-03 05:00:45.602 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-03 05:00:45.604 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\DotNetZip.dll
2025-07-03 05:00:45.605 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-03 05:00:45.606 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\ICSharpCode.SharpZipLib.dll
2025-07-03 05:00:45.607 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-03 05:00:45.608 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Vodia.CommonDomain.Model.dll
2025-07-03 05:00:45.609 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-03 05:00:45.611 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Vodia.Contracts.Common.dll
2025-07-03 05:00:45.612 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-03 05:00:45.613 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Vodia.UtilityComponent.dll
2025-07-03 05:00:45.614 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-03 05:00:45.615 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\log4net.dll
2025-07-03 05:00:45.617 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-03 05:00:45.618 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-03 05:00:45.619 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\AutoMapper.dll
2025-07-03 05:00:45.620 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 05:00:45.621 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.AppContext.dll
2025-07-03 05:00:45.622 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.AppContext.dll
2025-07-03 05:00:45.623 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Buffers.dll
2025-07-03 05:00:45.624 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Buffers.dll
2025-07-03 05:00:45.625 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-03 05:00:45.626 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.Concurrent.dll
2025-07-03 05:00:45.627 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.dll
2025-07-03 05:00:45.628 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.dll
2025-07-03 05:00:45.629 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-03 05:00:45.630 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.NonGeneric.dll
2025-07-03 05:00:45.631 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-03 05:00:45.632 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Collections.Specialized.dll
2025-07-03 05:00:45.633 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-03 05:00:45.633 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.dll
2025-07-03 05:00:45.634 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-03 05:00:45.635 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.EventBasedAsync.dll
2025-07-03 05:00:45.636 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-03 05:00:45.637 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.Primitives.dll
2025-07-03 05:00:45.638 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-03 05:00:45.639 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ComponentModel.TypeConverter.dll
2025-07-03 05:00:45.640 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Console.dll
2025-07-03 05:00:45.641 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Console.dll
2025-07-03 05:00:45.641 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-03 05:00:45.642 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Data.Common.dll
2025-07-03 05:00:45.643 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-03 05:00:45.644 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Data.SQLite.dll
2025-07-03 05:00:45.645 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-03 05:00:45.646 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Data.SqlServerCe.dll
2025-07-03 05:00:45.647 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-03 05:00:45.648 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Contracts.dll
2025-07-03 05:00:45.649 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-03 05:00:45.649 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Debug.dll
2025-07-03 05:00:45.651 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-03 05:00:45.652 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.FileVersionInfo.dll
2025-07-03 05:00:45.653 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-03 05:00:45.654 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Process.dll
2025-07-03 05:00:45.655 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-03 05:00:45.656 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.StackTrace.dll
2025-07-03 05:00:45.657 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-03 05:00:45.658 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.TextWriterTraceListener.dll
2025-07-03 05:00:45.658 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-03 05:00:45.659 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Tools.dll
2025-07-03 05:00:45.660 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-03 05:00:45.661 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.TraceSource.dll
2025-07-03 05:00:45.662 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-03 05:00:45.662 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Diagnostics.Tracing.dll
2025-07-03 05:00:45.663 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-03 05:00:45.665 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Drawing.Primitives.dll
2025-07-03 05:00:45.666 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-03 05:00:45.667 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Dynamic.Runtime.dll
2025-07-03 05:00:45.668 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-03 05:00:45.669 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Globalization.Calendars.dll
2025-07-03 05:00:45.670 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Globalization.dll
2025-07-03 05:00:45.671 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Globalization.dll
2025-07-03 05:00:45.672 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-03 05:00:45.673 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Globalization.Extensions.dll
2025-07-03 05:00:45.674 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-03 05:00:45.675 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.Compression.dll
2025-07-03 05:00:45.676 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-03 05:00:45.676 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.Compression.ZipFile.dll
2025-07-03 05:00:45.677 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.dll
2025-07-03 05:00:45.678 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.dll
2025-07-03 05:00:45.679 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-03 05:00:45.680 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.dll
2025-07-03 05:00:45.681 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-03 05:00:45.682 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.DriveInfo.dll
2025-07-03 05:00:45.683 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-03 05:00:45.684 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.Primitives.dll
2025-07-03 05:00:45.685 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-03 05:00:45.686 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.FileSystem.Watcher.dll
2025-07-03 05:00:45.686 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-03 05:00:45.687 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.IsolatedStorage.dll
2025-07-03 05:00:45.688 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-03 05:00:45.689 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.MemoryMappedFiles.dll
2025-07-03 05:00:45.689 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-03 05:00:45.690 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.Pipes.dll
2025-07-03 05:00:45.691 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-03 05:00:45.692 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.IO.UnmanagedMemoryStream.dll
2025-07-03 05:00:45.693 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.dll
2025-07-03 05:00:45.694 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.dll
2025-07-03 05:00:45.695 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-03 05:00:45.696 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.Expressions.dll
2025-07-03 05:00:45.697 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-03 05:00:45.698 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.Parallel.dll
2025-07-03 05:00:45.699 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-03 05:00:45.700 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Linq.Queryable.dll
2025-07-03 05:00:45.701 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Memory.dll
2025-07-03 05:00:45.701 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Memory.dll
2025-07-03 05:00:45.702 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-03 05:00:45.703 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Http.dll
2025-07-03 05:00:45.704 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-03 05:00:45.705 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.NameResolution.dll
2025-07-03 05:00:45.706 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-03 05:00:45.706 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.NetworkInformation.dll
2025-07-03 05:00:45.707 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-03 05:00:45.708 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Ping.dll
2025-07-03 05:00:45.709 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-03 05:00:45.710 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Primitives.dll
2025-07-03 05:00:45.711 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-03 05:00:45.712 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Requests.dll
2025-07-03 05:00:45.713 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-03 05:00:45.714 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Security.dll
2025-07-03 05:00:45.715 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-03 05:00:45.716 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.Sockets.dll
2025-07-03 05:00:45.717 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-03 05:00:45.718 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.WebHeaderCollection.dll
2025-07-03 05:00:45.718 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-03 05:00:45.719 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.WebSockets.Client.dll
2025-07-03 05:00:45.720 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-03 05:00:45.721 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Net.WebSockets.dll
2025-07-03 05:00:45.722 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-03 05:00:45.722 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Numerics.Vectors.dll
2025-07-03 05:00:45.723 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-03 05:00:45.724 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ObjectModel.dll
2025-07-03 05:00:45.725 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Reflection.dll
2025-07-03 05:00:45.726 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Reflection.dll
2025-07-03 05:00:45.727 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-03 05:00:45.728 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Reflection.Extensions.dll
2025-07-03 05:00:45.729 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-03 05:00:45.730 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Reflection.Primitives.dll
2025-07-03 05:00:45.731 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-03 05:00:45.732 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Resources.Reader.dll
2025-07-03 05:00:45.733 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-03 05:00:45.734 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Resources.ResourceManager.dll
2025-07-03 05:00:45.734 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-03 05:00:45.735 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Resources.Writer.dll
2025-07-03 05:00:45.736 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-03 05:00:45.737 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.CompilerServices.Unsafe.dll
2025-07-03 05:00:45.738 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-03 05:00:45.738 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.CompilerServices.VisualC.dll
2025-07-03 05:00:45.739 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.dll
2025-07-03 05:00:45.740 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.dll
2025-07-03 05:00:45.742 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-03 05:00:45.743 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Extensions.dll
2025-07-03 05:00:45.743 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-03 05:00:45.744 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Handles.dll
2025-07-03 05:00:45.745 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-03 05:00:45.746 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.InteropServices.dll
2025-07-03 05:00:45.747 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-03 05:00:45.747 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-03 05:00:45.748 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-03 05:00:45.749 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Numerics.dll
2025-07-03 05:00:45.750 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-03 05:00:45.751 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Formatters.dll
2025-07-03 05:00:45.752 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-03 05:00:45.752 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Json.dll
2025-07-03 05:00:45.753 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-03 05:00:45.754 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Primitives.dll
2025-07-03 05:00:45.755 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-03 05:00:45.755 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Runtime.Serialization.Xml.dll
2025-07-03 05:00:45.756 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-03 05:00:45.757 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Claims.dll
2025-07-03 05:00:45.758 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-03 05:00:45.759 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Algorithms.dll
2025-07-03 05:00:45.760 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-03 05:00:45.761 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Csp.dll
2025-07-03 05:00:45.762 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-03 05:00:45.763 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Encoding.dll
2025-07-03 05:00:45.764 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-03 05:00:45.765 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.Primitives.dll
2025-07-03 05:00:45.766 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-03 05:00:45.773 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Cryptography.X509Certificates.dll
2025-07-03 05:00:45.774 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-03 05:00:45.775 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.Principal.dll
2025-07-03 05:00:45.776 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-03 05:00:45.777 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Security.SecureString.dll
2025-07-03 05:00:45.778 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-03 05:00:45.779 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Text.Encoding.dll
2025-07-03 05:00:45.780 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-03 05:00:45.781 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Text.Encoding.Extensions.dll
2025-07-03 05:00:45.782 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-03 05:00:45.783 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Text.RegularExpressions.dll
2025-07-03 05:00:45.784 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.dll
2025-07-03 05:00:45.785 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.dll
2025-07-03 05:00:45.786 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-03 05:00:45.786 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Overlapped.dll
2025-07-03 05:00:45.787 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-03 05:00:45.788 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Tasks.dll
2025-07-03 05:00:45.788 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-03 05:00:45.789 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Tasks.Extensions.dll
2025-07-03 05:00:45.790 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-03 05:00:45.792 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Tasks.Parallel.dll
2025-07-03 05:00:45.793 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-03 05:00:45.794 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Thread.dll
2025-07-03 05:00:45.794 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-03 05:00:45.795 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.ThreadPool.dll
2025-07-03 05:00:45.796 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-03 05:00:45.797 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Threading.Timer.dll
2025-07-03 05:00:45.798 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-03 05:00:45.799 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.ValueTuple.dll
2025-07-03 05:00:45.800 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-03 05:00:45.802 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.ReaderWriter.dll
2025-07-03 05:00:45.803 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-03 05:00:45.804 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XDocument.dll
2025-07-03 05:00:45.805 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-03 05:00:45.806 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XmlDocument.dll
2025-07-03 05:00:45.807 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-03 05:00:45.808 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XmlSerializer.dll
2025-07-03 05:00:45.809 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-03 05:00:45.810 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XPath.dll
2025-07-03 05:00:45.811 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-03 05:00:45.812 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\System.Xml.XPath.XDocument.dll
2025-07-03 05:00:45.813 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Phoenix\System\SystemInterface.dll
2025-07-03 05:00:45.814 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\SystemInterface.dll
2025-07-03 05:00:45.814 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-03 05:00:45.815 [Information] PhoenixVocomAdapter: Loading APCI library dynamically
2025-07-03 05:00:45.875 [Error] PhoenixVocomAdapter: Failed to load APCI library. Error code: 193
2025-07-03 05:00:45.876 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-03 05:00:45.876 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-03 05:00:45.877 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-03 05:00:45.878 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-03 05:00:45.878 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-03 05:00:45.879 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-03 05:00:45.880 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-03 05:00:45.881 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 05:00:45.881 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-03 05:00:45.882 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-03 05:00:45.883 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-03 05:00:45.883 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-03 05:00:45.884 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-03 05:00:45.884 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-03 05:00:45.884 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-03 05:00:45.884 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-03 05:00:45.885 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-03 05:00:45.886 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-03 05:00:45.897 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-03 05:00:45.906 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-03 05:00:45.906 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-03 05:00:45.918 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-03 05:00:45.919 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-03 05:00:45.919 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 05:00:45.920 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-03 05:00:45.920 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-03 05:00:45.921 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-03 05:00:45.922 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-03 05:00:45.923 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-03 05:00:45.923 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-03 05:00:45.930 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-03 05:00:45.931 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-03 05:00:45.931 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-07-03 05:00:45.931 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-07-03 05:00:45.933 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-03 05:00:45.934 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 05:00:45.991 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:45.991 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-03 05:00:46.035 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 05:00:46.078 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:46.079 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-03 05:00:46.228 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:46.229 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-03 05:00:46.303 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:46.304 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-03 05:00:46.392 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 05:00:46.473 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:46.474 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-03 05:00:46.555 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 05:00:46.648 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:46.649 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-03 05:00:46.743 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 05:00:46.887 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 05:00:46.983 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 05:00:47.073 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:47.074 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-03 05:00:47.121 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries
2025-07-03 05:00:47.305 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:47.305 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-03 05:00:47.371 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 05:00:47.436 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:47.436 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-03 05:00:47.551 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\, error: 0
2025-07-03 05:00:47.658 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries, error: 0
2025-07-03 05:00:47.658 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-03 05:00:47.762 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 05:00:47.770 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 05:00:47.814 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\
2025-07-03 05:00:47.817 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-03 05:00:47.817 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\apci.dll
2025-07-03 05:00:47.817 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-03 05:00:47.818 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-03 05:00:47.818 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-03 05:00:47.819 [Information] VocomDriver: Initializing Vocom driver
2025-07-03 05:00:47.820 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-03 05:00:47.821 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-03 05:00:47.822 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 05:00:47.822 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 05:00:47.823 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 05:00:47.823 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-03 05:00:47.825 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcr120.dll
2025-07-03 05:00:47.825 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp120.dll
2025-07-03 05:00:47.826 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-03 05:00:47.827 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp140.dll
2025-07-03 05:00:47.828 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\vcruntime140.dll
2025-07-03 05:00:47.828 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 05:00:47.830 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-03 05:00:47.830 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-03 05:00:47.831 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-03 05:00:47.832 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-03 05:00:47.832 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-03 05:00:47.833 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-03 05:00:47.834 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-03 05:00:47.834 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-03 05:00:47.835 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-03 05:00:47.835 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-03 05:00:47.835 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-03 05:00:47.835 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-03 05:00:47.836 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-03 05:00:47.836 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-03 05:00:47.836 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-03 05:00:47.837 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-03 05:00:47.837 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-03 05:00:47.837 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-03 05:00:47.837 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-03 05:00:47.837 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-03 05:00:47.838 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-03 05:00:47.838 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-03 05:00:47.838 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-03 05:00:47.838 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-03 05:00:47.839 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-03 05:00:47.839 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-03 05:00:47.839 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-03 05:00:47.839 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-03 05:00:47.839 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-03 05:00:47.839 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-03 05:00:47.840 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-03 05:00:47.840 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-03 05:00:47.840 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-03 05:00:47.840 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-03 05:00:47.840 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-03 05:00:47.840 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-03 05:00:47.840 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-03 05:00:47.841 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-03 05:00:47.841 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-03 05:00:47.841 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-03 05:00:47.841 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-03 05:00:47.841 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-03 05:00:47.842 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-03 05:00:47.842 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-03 05:00:47.842 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-03 05:00:47.842 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-03 05:00:47.842 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-03 05:00:47.842 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-03 05:00:47.843 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-03 05:00:47.843 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-03 05:00:47.844 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-03 05:00:47.844 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-03 05:00:47.845 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-03 05:00:47.845 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-03 05:00:47.845 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-03 05:00:47.845 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-03 05:00:47.845 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-03 05:00:47.847 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-03 05:00:47.848 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-03 05:00:47.849 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-03 05:00:47.850 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-03 05:00:47.903 [Information] WiFiCommunicationService: WiFi is not available
2025-07-03 05:00:47.904 [Warning] WiFiCommunicationService: WiFi is not available, attempting to enable it
2025-07-03 05:00:47.905 [Information] WiFiCommunicationService: Enabling WiFi
2025-07-03 05:00:47.905 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-03 05:00:47.947 [Information] WiFiCommunicationService: WiFi is not available
2025-07-03 05:00:47.948 [Information] WiFiCommunicationService: WiFi enabled successfully
2025-07-03 05:00:47.948 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-03 05:00:47.950 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-03 05:00:47.951 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-03 05:00:47.952 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-03 05:00:47.952 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-03 05:00:47.953 [Information] VocomService: Initializing Vocom service
2025-07-03 05:00:47.955 [Information] VocomService: Checking if PTT application is running
2025-07-03 05:00:47.963 [Information] VocomService: PTT application is not running
2025-07-03 05:00:47.964 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-03 05:00:47.966 [Debug] VocomService: Bluetooth is enabled
2025-07-03 05:00:47.966 [Information] VocomService: Vocom service initialized successfully
2025-07-03 05:00:47.967 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-03 05:00:47.967 [Information] App: Initializing Vocom service
2025-07-03 05:00:47.967 [Information] VocomService: Initializing Vocom service
2025-07-03 05:00:47.967 [Information] VocomService: Checking if PTT application is running
2025-07-03 05:00:47.975 [Information] VocomService: PTT application is not running
2025-07-03 05:00:47.975 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-03 05:00:47.975 [Debug] VocomService: Bluetooth is enabled
2025-07-03 05:00:47.976 [Information] VocomService: Vocom service initialized successfully
2025-07-03 05:00:47.977 [Information] VocomService: Scanning for Vocom devices
2025-07-03 05:00:47.985 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-07-03 05:00:48.005 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-07-03 05:00:48.005 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-03 05:00:48.005 [Debug] VocomService: Bluetooth is enabled
2025-07-03 05:00:48.007 [Debug] VocomService: Checking if WiFi is available
2025-07-03 05:00:48.008 [Debug] VocomService: WiFi is available
2025-07-03 05:00:48.008 [Information] VocomService: Found 2 Vocom devices
2025-07-03 05:00:48.009 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-07-03 05:00:48.010 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-03 05:00:48.011 [Information] VocomService: Checking if PTT application is running
2025-07-03 05:00:48.019 [Information] VocomService: PTT application is not running
2025-07-03 05:00:48.021 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-03 05:00:48.021 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-03 05:00:48.021 [Debug] VocomService: Bluetooth is enabled
2025-07-03 05:00:48.022 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-03 05:00:48.824 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-03 05:00:48.825 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-03 05:00:48.825 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-07-03 05:00:48.827 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-03 05:00:48.829 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:48.829 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-03 05:00:48.831 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 05:00:48.833 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 05:00:48.833 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 05:00:48.834 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 05:00:48.835 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 05:00:48.839 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 05:00:48.840 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 05:00:48.842 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 05:00:48.846 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 05:00:48.849 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 05:00:48.861 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 05:00:48.861 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-03 05:00:48.862 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-03 05:00:48.862 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-03 05:00:48.862 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-03 05:00:48.862 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-03 05:00:48.863 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-03 05:00:48.863 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-03 05:00:48.863 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-03 05:00:48.865 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-03 05:00:48.866 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-03 05:00:48.866 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-03 05:00:48.866 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-03 05:00:48.867 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-03 05:00:48.867 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-03 05:00:48.867 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-03 05:00:48.867 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-03 05:00:48.869 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-03 05:00:48.875 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-03 05:00:48.875 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-03 05:00:48.877 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-03 05:00:48.878 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:48.884 [Information] CANRegisterAccess: Read value 0xFC from register 0x0141 (simulated)
2025-07-03 05:00:48.889 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:48.895 [Information] CANRegisterAccess: Read value 0x82 from register 0x0141 (simulated)
2025-07-03 05:00:48.901 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:48.907 [Information] CANRegisterAccess: Read value 0xBB from register 0x0141 (simulated)
2025-07-03 05:00:48.908 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-03 05:00:48.908 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-03 05:00:48.908 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-03 05:00:48.914 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-03 05:00:48.915 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-03 05:00:48.920 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-03 05:00:48.921 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-03 05:00:48.921 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-03 05:00:48.925 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-03 05:00:48.926 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-03 05:00:48.926 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-03 05:00:48.932 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-03 05:00:48.933 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-03 05:00:48.938 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-03 05:00:48.938 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-03 05:00:48.943 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-03 05:00:48.943 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-03 05:00:48.949 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-03 05:00:48.949 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-03 05:00:48.954 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-03 05:00:48.954 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-03 05:00:48.960 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-03 05:00:48.960 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-03 05:00:48.966 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-03 05:00:48.967 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-03 05:00:48.972 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-03 05:00:48.972 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-03 05:00:48.977 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-03 05:00:48.978 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-03 05:00:48.983 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-03 05:00:48.983 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-03 05:00:48.989 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-03 05:00:48.989 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-03 05:00:48.995 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-03 05:00:48.995 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-03 05:00:49.001 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-03 05:00:49.001 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-03 05:00:49.007 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-03 05:00:49.007 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-03 05:00:49.012 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-03 05:00:49.012 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-03 05:00:49.018 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-03 05:00:49.018 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-03 05:00:49.024 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-03 05:00:49.024 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-03 05:00:49.024 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-03 05:00:49.029 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-03 05:00:49.029 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-03 05:00:49.030 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-03 05:00:49.030 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:49.035 [Information] CANRegisterAccess: Read value 0xC5 from register 0x0141 (simulated)
2025-07-03 05:00:49.041 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:49.047 [Information] CANRegisterAccess: Read value 0x5B from register 0x0141 (simulated)
2025-07-03 05:00:49.053 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:49.059 [Information] CANRegisterAccess: Read value 0xCB from register 0x0141 (simulated)
2025-07-03 05:00:49.064 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:49.070 [Information] CANRegisterAccess: Read value 0x57 from register 0x0141 (simulated)
2025-07-03 05:00:49.076 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:49.082 [Information] CANRegisterAccess: Read value 0x5C from register 0x0141 (simulated)
2025-07-03 05:00:49.082 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-03 05:00:49.082 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-03 05:00:49.083 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-03 05:00:49.083 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 05:00:49.088 [Information] CANRegisterAccess: Read value 0xCF from register 0x0140 (simulated)
2025-07-03 05:00:49.094 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 05:00:49.100 [Information] CANRegisterAccess: Read value 0x1D from register 0x0140 (simulated)
2025-07-03 05:00:49.100 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-03 05:00:49.100 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 05:00:49.103 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 05:00:49.103 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 05:00:49.114 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-03 05:00:49.114 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-03 05:00:49.115 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-03 05:00:49.118 [Information] VocomService: Sending data and waiting for response
2025-07-03 05:00:49.118 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-03 05:00:49.170 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-03 05:00:49.171 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-03 05:00:49.171 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-03 05:00:49.172 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 05:00:49.173 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 05:00:49.183 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 05:00:49.183 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-03 05:00:49.184 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-03 05:00:49.194 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-03 05:00:49.205 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-03 05:00:49.216 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-03 05:00:49.226 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-03 05:00:49.237 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 05:00:49.238 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 05:00:49.238 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 05:00:49.249 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 05:00:49.249 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-03 05:00:49.250 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-03 05:00:49.260 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-03 05:00:49.270 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-03 05:00:49.281 [Information] IICProtocolHandler: Enabling IIC module
2025-07-03 05:00:49.292 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-03 05:00:49.303 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-03 05:00:49.314 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 05:00:49.316 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 05:00:49.316 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 05:00:49.327 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 05:00:49.328 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-03 05:00:49.328 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-03 05:00:49.329 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-03 05:00:49.329 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-03 05:00:49.329 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-03 05:00:49.329 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-03 05:00:49.329 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-03 05:00:49.330 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-03 05:00:49.330 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-03 05:00:49.330 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-03 05:00:49.330 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-03 05:00:49.330 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-03 05:00:49.331 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-03 05:00:49.331 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-03 05:00:49.331 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-03 05:00:49.331 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-03 05:00:49.431 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 05:00:49.432 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 05:00:49.434 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 05:00:49.435 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:49.435 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 05:00:49.436 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 05:00:49.436 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:49.436 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 05:00:49.436 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 05:00:49.437 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:49.437 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 05:00:49.437 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 05:00:49.437 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:49.438 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 05:00:49.438 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 05:00:49.439 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-03 05:00:49.439 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-03 05:00:49.439 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-03 05:00:49.441 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-03 05:00:49.442 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-03 05:00:49.445 [Information] BackupService: Initializing backup service
2025-07-03 05:00:49.445 [Information] BackupService: Backup service initialized successfully
2025-07-03 05:00:49.446 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-03 05:00:49.446 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-03 05:00:49.447 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-03 05:00:49.469 [Information] BackupService: Compressing backup data
2025-07-03 05:00:49.475 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-03 05:00:49.476 [Information] BackupServiceFactory: Created template for category: Production
2025-07-03 05:00:49.476 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-03 05:00:49.477 [Information] BackupService: Compressing backup data
2025-07-03 05:00:49.478 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (446 bytes)
2025-07-03 05:00:49.478 [Information] BackupServiceFactory: Created template for category: Development
2025-07-03 05:00:49.478 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-03 05:00:49.479 [Information] BackupService: Compressing backup data
2025-07-03 05:00:49.480 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (442 bytes)
2025-07-03 05:00:49.480 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-03 05:00:49.480 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-03 05:00:49.481 [Information] BackupService: Compressing backup data
2025-07-03 05:00:49.482 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-07-03 05:00:49.482 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-03 05:00:49.482 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-03 05:00:49.483 [Information] BackupService: Compressing backup data
2025-07-03 05:00:49.484 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-03 05:00:49.484 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-03 05:00:49.485 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-03 05:00:49.486 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-03 05:00:49.486 [Information] BackupService: Compressing backup data
2025-07-03 05:00:49.487 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-03 05:00:49.488 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-03 05:00:49.488 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-03 05:00:49.491 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-03 05:00:49.494 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 05:00:49.496 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 05:00:49.556 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-07-03 05:00:49.557 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 05:00:49.558 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-03 05:00:49.558 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-03 05:00:49.558 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-03 05:00:49.559 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-03 05:00:49.560 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-03 05:00:49.563 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-03 05:00:49.563 [Information] App: Flash operation monitor service initialized successfully
2025-07-03 05:00:49.573 [Information] LicensingService: Initializing licensing service
2025-07-03 05:00:49.627 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-03 05:00:49.629 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-03 05:00:49.630 [Information] App: Licensing service initialized successfully
2025-07-03 05:00:49.630 [Information] App: License status: Trial
2025-07-03 05:00:49.630 [Information] App: Trial period: 30 days remaining
2025-07-03 05:00:49.631 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-03 05:00:49.651 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-03 05:00:49.793 [Information] VocomService: Initializing Vocom service
2025-07-03 05:00:49.794 [Information] VocomService: Checking if PTT application is running
2025-07-03 05:00:49.802 [Information] VocomService: PTT application is not running
2025-07-03 05:00:49.802 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-03 05:00:49.803 [Debug] VocomService: Bluetooth is enabled
2025-07-03 05:00:49.803 [Information] VocomService: Vocom service initialized successfully
2025-07-03 05:00:49.854 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 05:00:49.854 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 05:00:49.855 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 05:00:49.855 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 05:00:49.855 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 05:00:49.856 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 05:00:49.856 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 05:00:49.857 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 05:00:49.857 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 05:00:49.858 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 05:00:49.867 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 05:00:49.867 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-03 05:00:49.868 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-03 05:00:49.868 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-03 05:00:49.868 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-03 05:00:49.868 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-03 05:00:49.869 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-03 05:00:49.869 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-03 05:00:49.869 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-03 05:00:49.869 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-03 05:00:49.870 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-03 05:00:49.870 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-03 05:00:49.870 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-03 05:00:49.870 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-03 05:00:49.871 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-03 05:00:49.871 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-03 05:00:49.871 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-03 05:00:49.871 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-03 05:00:49.876 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-03 05:00:49.876 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-03 05:00:49.877 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-03 05:00:49.877 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:49.882 [Information] CANRegisterAccess: Read value 0x99 from register 0x0141 (simulated)
2025-07-03 05:00:49.882 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-03 05:00:49.883 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-03 05:00:49.883 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-03 05:00:49.888 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-03 05:00:49.888 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-03 05:00:49.893 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-03 05:00:49.894 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-03 05:00:49.894 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-03 05:00:49.900 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-03 05:00:49.900 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-03 05:00:49.901 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-03 05:00:49.906 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-03 05:00:49.906 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-03 05:00:49.911 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-03 05:00:49.911 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-03 05:00:49.918 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-03 05:00:49.918 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-03 05:00:49.924 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-03 05:00:49.924 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-03 05:00:49.930 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-03 05:00:49.930 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-03 05:00:49.936 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-03 05:00:49.936 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-03 05:00:49.942 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-03 05:00:49.942 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-03 05:00:49.948 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-03 05:00:49.948 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-03 05:00:49.955 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-03 05:00:49.955 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-03 05:00:49.962 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-03 05:00:49.962 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-03 05:00:49.969 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-03 05:00:49.969 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-03 05:00:49.975 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-03 05:00:49.975 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-03 05:00:49.981 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-03 05:00:49.981 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-03 05:00:49.988 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-03 05:00:49.988 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-03 05:00:49.994 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-03 05:00:49.994 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-03 05:00:50.000 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-03 05:00:50.000 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-03 05:00:50.006 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-03 05:00:50.006 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-03 05:00:50.006 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-03 05:00:50.013 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-03 05:00:50.013 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-03 05:00:50.013 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-03 05:00:50.014 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:50.020 [Information] CANRegisterAccess: Read value 0x7B from register 0x0141 (simulated)
2025-07-03 05:00:50.026 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:50.032 [Information] CANRegisterAccess: Read value 0xB3 from register 0x0141 (simulated)
2025-07-03 05:00:50.037 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 05:00:50.044 [Information] CANRegisterAccess: Read value 0xBC from register 0x0141 (simulated)
2025-07-03 05:00:50.044 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-03 05:00:50.044 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-03 05:00:50.044 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-03 05:00:50.045 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 05:00:50.051 [Information] CANRegisterAccess: Read value 0xDE from register 0x0140 (simulated)
2025-07-03 05:00:50.051 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-03 05:00:50.051 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 05:00:50.051 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 05:00:50.052 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 05:00:50.062 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-03 05:00:50.063 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-03 05:00:50.063 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-03 05:00:50.063 [Information] VocomService: Sending data and waiting for response
2025-07-03 05:00:50.063 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-03 05:00:50.115 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-03 05:00:50.116 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-03 05:00:50.117 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-03 05:00:50.117 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 05:00:50.117 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 05:00:50.128 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 05:00:50.128 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-03 05:00:50.128 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-03 05:00:50.140 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-03 05:00:50.151 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-03 05:00:50.162 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-03 05:00:50.173 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-03 05:00:50.183 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 05:00:50.184 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 05:00:50.184 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 05:00:50.195 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 05:00:50.196 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-03 05:00:50.196 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-03 05:00:50.206 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-03 05:00:50.217 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-03 05:00:50.228 [Information] IICProtocolHandler: Enabling IIC module
2025-07-03 05:00:50.239 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-03 05:00:50.250 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-03 05:00:50.261 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 05:00:50.262 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 05:00:50.262 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 05:00:50.272 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 05:00:50.273 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-03 05:00:50.273 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-03 05:00:50.273 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-03 05:00:50.273 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-03 05:00:50.274 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-03 05:00:50.274 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-03 05:00:50.274 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-03 05:00:50.274 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-03 05:00:50.274 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-03 05:00:50.275 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-03 05:00:50.275 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-03 05:00:50.275 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-03 05:00:50.276 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-03 05:00:50.276 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-03 05:00:50.276 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-03 05:00:50.276 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-03 05:00:50.376 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 05:00:50.377 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 05:00:50.377 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 05:00:50.377 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:50.378 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 05:00:50.378 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 05:00:50.378 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:50.378 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 05:00:50.378 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 05:00:50.379 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:50.379 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 05:00:50.379 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 05:00:50.379 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 05:00:50.379 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 05:00:50.380 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 05:00:50.430 [Information] BackupService: Initializing backup service
2025-07-03 05:00:50.431 [Information] BackupService: Backup service initialized successfully
2025-07-03 05:00:50.481 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 05:00:50.481 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 05:00:50.482 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-07-03 05:00:50.483 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 05:00:50.534 [Information] BackupService: Getting predefined backup categories
2025-07-03 05:00:50.586 [Information] MainViewModel: Services initialized successfully
2025-07-03 05:00:50.587 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 05:00:50.588 [Information] VocomService: Scanning for Vocom devices
2025-07-03 05:00:50.588 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-07-03 05:00:50.589 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-07-03 05:00:50.589 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-03 05:00:50.590 [Debug] VocomService: Bluetooth is enabled
2025-07-03 05:00:50.590 [Debug] VocomService: Checking if WiFi is available
2025-07-03 05:00:50.591 [Debug] VocomService: WiFi is available
2025-07-03 05:00:50.591 [Information] VocomService: Found 2 Vocom devices
2025-07-03 05:00:50.592 [Information] MainViewModel: Found 2 Vocom device(s)
2025-07-03 05:28:03.925 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 05:28:03.927 [Information] VocomService: Scanning for Vocom devices
2025-07-03 05:28:03.928 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-07-03 05:28:03.928 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-07-03 05:28:03.929 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-03 05:28:03.930 [Debug] VocomService: Bluetooth is enabled
2025-07-03 05:28:03.930 [Debug] VocomService: Checking if WiFi is available
2025-07-03 05:28:03.931 [Debug] VocomService: WiFi is available
2025-07-03 05:28:03.931 [Information] VocomService: Found 2 Vocom devices
2025-07-03 05:28:03.931 [Information] MainViewModel: Found 2 Vocom device(s)
