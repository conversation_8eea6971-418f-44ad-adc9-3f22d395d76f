using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Manages dependencies and libraries required for Vocom adapter communication
    /// Automatically handles library loading and dependency resolution
    /// </summary>
    public class DependencyManager
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly string _driversPath;
        private bool _isInitialized = false;

        // Windows API imports for library management
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetDllDirectory(string lpPathName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool AddDllDirectory(string newDirectory);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        // Critical libraries for Vocom communication
        private static readonly string[] CriticalLibraries = new[]
        {
            "WUDFPuma.dll",
            "apci.dll",
            "apcidb.dll",
            "Volvo.ApciPlus.dll",
            "Volvo.ApciPlusData.dll",
            "PhoenixGeneral.dll",
            "msvcr120.dll",
            "msvcp120.dll",
            "msvcr140.dll",
            "msvcp140.dll",
            "vcruntime140.dll"
        };

        // Visual C++ Redistributable libraries
        private static readonly string[] VCRedistLibraries = new[]
        {
            "msvcr120.dll",
            "msvcp120.dll",
            "msvcr140.dll",
            "msvcp140.dll",
            "vcruntime140.dll",
            "api-ms-win-crt-runtime-l1-1-0.dll",
            "api-ms-win-crt-heap-l1-1-0.dll",
            "api-ms-win-crt-string-l1-1-0.dll"
        };

        public DependencyManager(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _driversPath = Path.Combine(_applicationPath, "Drivers", "Vocom");
        }

        /// <summary>
        /// Initializes the dependency manager and loads all required libraries
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            if (_isInitialized)
                return true;

            try
            {
                _logger.LogInformation("Initializing dependency manager", "DependencyManager");

                // Step 1: Setup library search paths
                SetupLibraryPaths();

                // Step 2: Verify critical directories exist
                await VerifyDirectoriesAsync();

                // Step 3: Load Visual C++ Redistributables first
                await LoadVCRedistLibrariesAsync();

                // Step 4: Load critical Vocom libraries
                await LoadCriticalLibrariesAsync();

                // Step 5: Setup environment variables
                SetupEnvironmentVariables();

                // Step 6: Verify library loading
                await VerifyLibraryLoadingAsync();

                _isInitialized = true;
                _logger.LogInformation("Dependency manager initialized successfully", "DependencyManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to initialize dependency manager: {ex.Message}", "DependencyManager", ex);
                return false;
            }
        }

        private void SetupLibraryPaths()
        {
            try
            {
                _logger.LogInformation("Setting up library search paths", "DependencyManager");

                // Add our libraries directory to DLL search path
                if (Directory.Exists(_librariesPath))
                {
                    SetDllDirectory(_librariesPath);
                    AddDllDirectory(_librariesPath);
                    _logger.LogInformation($"Added library path: {_librariesPath}", "DependencyManager");
                }

                // Add drivers directory to DLL search path
                if (Directory.Exists(_driversPath))
                {
                    AddDllDirectory(_driversPath);
                    _logger.LogInformation($"Added driver path: {_driversPath}", "DependencyManager");
                }

                // Add application directory
                AddDllDirectory(_applicationPath);
                _logger.LogInformation($"Added application path: {_applicationPath}", "DependencyManager");

                // Update PATH environment variable
                string currentPath = Environment.GetEnvironmentVariable("PATH") ?? "";
                string newPath = $"{_librariesPath};{_driversPath};{_applicationPath};{currentPath}";
                Environment.SetEnvironmentVariable("PATH", newPath);
                _logger.LogInformation("Updated PATH environment variable", "DependencyManager");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error setting up library paths: {ex.Message}", "DependencyManager");
            }
        }

        private async Task VerifyDirectoriesAsync()
        {
            _logger.LogInformation("Verifying required directories", "DependencyManager");

            var requiredDirectories = new[]
            {
                _librariesPath,
                _driversPath,
                Path.Combine(_librariesPath, "System"),
                Path.Combine(_applicationPath, "Config")
            };

            foreach (string directory in requiredDirectories)
            {
                if (!Directory.Exists(directory))
                {
                    _logger.LogWarning($"Required directory not found: {directory}", "DependencyManager");
                    try
                    {
                        Directory.CreateDirectory(directory);
                        _logger.LogInformation($"Created directory: {directory}", "DependencyManager");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Failed to create directory {directory}: {ex.Message}", "DependencyManager");
                    }
                }
                else
                {
                    _logger.LogInformation($"✓ Directory exists: {directory}", "DependencyManager");
                }
            }

            await Task.CompletedTask;
        }

        private async Task LoadVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Loading Visual C++ Redistributable libraries", "DependencyManager");

            foreach (string library in VCRedistLibraries)
            {
                await LoadLibraryAsync(library, "VC++ Redistributable");
            }
        }

        private async Task LoadCriticalLibrariesAsync()
        {
            _logger.LogInformation("Loading critical Vocom libraries", "DependencyManager");

            foreach (string library in CriticalLibraries)
            {
                await LoadLibraryAsync(library, "Critical");
            }
        }

        private async Task<bool> LoadLibraryAsync(string libraryName, string category)
        {
            try
            {
                // Search for the library in multiple locations
                var searchPaths = new[]
                {
                    Path.Combine(_librariesPath, libraryName),
                    Path.Combine(_driversPath, libraryName),
                    Path.Combine(_applicationPath, libraryName),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), libraryName),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.SystemX86), libraryName)
                };

                string foundPath = null;
                foreach (string path in searchPaths)
                {
                    if (File.Exists(path))
                    {
                        foundPath = path;
                        break;
                    }
                }

                if (foundPath == null)
                {
                    _logger.LogWarning($"{category} library not found: {libraryName}", "DependencyManager");
                    return false;
                }

                // Try to load the library
                IntPtr handle = LoadLibrary(foundPath);
                if (handle == IntPtr.Zero)
                {
                    int errorCode = Marshal.GetLastWin32Error();
                    _logger.LogWarning($"Failed to load {category} library {libraryName}: Error {errorCode}", "DependencyManager");
                    return false;
                }

                _logger.LogInformation($"✓ Loaded {category} library: {libraryName} from {foundPath}", "DependencyManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception loading {category} library {libraryName}: {ex.Message}", "DependencyManager");
                return false;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        private void SetupEnvironmentVariables()
        {
            try
            {
                _logger.LogInformation("Setting up environment variables", "DependencyManager");

                // Set library paths
                Environment.SetEnvironmentVariable("APCI_LIBRARY_PATH", _librariesPath);
                Environment.SetEnvironmentVariable("VOCOM_DRIVER_PATH", _driversPath);
                Environment.SetEnvironmentVariable("USE_PATCHED_IMPLEMENTATION", "true");
                Environment.SetEnvironmentVariable("PHOENIX_VOCOM_ENABLED", "true");
                Environment.SetEnvironmentVariable("VERBOSE_LOGGING", "true");

                _logger.LogInformation("Environment variables configured", "DependencyManager");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error setting environment variables: {ex.Message}", "DependencyManager");
            }
        }

        private async Task VerifyLibraryLoadingAsync()
        {
            _logger.LogInformation("Verifying library loading status", "DependencyManager");

            int loadedCount = 0;
            int totalCount = CriticalLibraries.Length;

            foreach (string library in CriticalLibraries)
            {
                if (await IsLibraryLoadedAsync(library))
                {
                    loadedCount++;
                }
            }

            double successRate = (double)loadedCount / totalCount * 100;
            _logger.LogInformation($"Library loading verification: {loadedCount}/{totalCount} ({successRate:F1}%) critical libraries loaded", "DependencyManager");

            if (successRate < 70)
            {
                _logger.LogWarning("Low library loading success rate - some functionality may be limited", "DependencyManager");
            }
        }

        private async Task<bool> IsLibraryLoadedAsync(string libraryName)
        {
            try
            {
                // Try to find the library in loaded modules
                var processes = System.Diagnostics.Process.GetProcessesByName(System.Diagnostics.Process.GetCurrentProcess().ProcessName);
                foreach (var process in processes)
                {
                    try
                    {
                        foreach (System.Diagnostics.ProcessModule module in process.Modules)
                        {
                            if (module.ModuleName.Equals(libraryName, StringComparison.OrdinalIgnoreCase))
                            {
                                return true;
                            }
                        }
                    }
                    catch
                    {
                        // Ignore access denied errors
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"Error checking if library {libraryName} is loaded: {ex.Message}", "DependencyManager");
            }

            await Task.CompletedTask;
            return false;
        }

        /// <summary>
        /// Gets the status of dependency loading
        /// </summary>
        public async Task<DependencyStatus> GetStatusAsync()
        {
            var status = new DependencyStatus
            {
                IsInitialized = _isInitialized,
                LibrariesPath = _librariesPath,
                DriversPath = _driversPath,
                CriticalLibrariesFound = new List<string>(),
                MissingLibraries = new List<string>()
            };

            foreach (string library in CriticalLibraries)
            {
                var searchPaths = new[]
                {
                    Path.Combine(_librariesPath, library),
                    Path.Combine(_driversPath, library),
                    Path.Combine(_applicationPath, library)
                };

                bool found = false;
                foreach (string path in searchPaths)
                {
                    if (File.Exists(path))
                    {
                        status.CriticalLibrariesFound.Add($"{library} ({path})");
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    status.MissingLibraries.Add(library);
                }
            }

            await Task.CompletedTask;
            return status;
        }
    }

    /// <summary>
    /// Represents the status of dependency loading
    /// </summary>
    public class DependencyStatus
    {
        public bool IsInitialized { get; set; }
        public string LibrariesPath { get; set; } = string.Empty;
        public string DriversPath { get; set; } = string.Empty;
        public List<string> CriticalLibrariesFound { get; set; } = new();
        public List<string> MissingLibraries { get; set; } = new();
    }
}
